"use client";

import { format } from "date-fns";
import { CalendarIcon, Flag } from "lucide-react";
import { Popover as HeadlessPopover } from '@headlessui/react';
import { DateRange } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import { Checkbox } from "@radix-ui/react-checkbox";

interface FilterParams {
  search?: string;
  dateFrom?: string | Date;
  dateTo?: string | Date;
  status?: "active" | "deleted" | "all";
}

interface InvoiceFileFiltersProps {
  filters: FilterParams;
  onFiltersChange: (filters: FilterParams) => void;
}

const statusOptions = [
  { value: "all", label: "All Files" },
  { value: "active", label: "Active Only" },
  { value: "deleted", label: "Deleted Only" },
];

export function InvoiceFileFilters({ filters, onFiltersChange }: InvoiceFileFiltersProps) {
  const updateFilters = (updates: Partial<FilterParams>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: "",
      dateFrom: undefined,
      dateTo: undefined,
      status: "all",
    });
  };

  return (
    <div className="space-y-4">
      <div
        className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 my-3"
        style={{ minWidth: 380 }}
      >
        {/* Filters Row */}
        <div className="flex flex-wrap gap-4">
          {/* Date Range Filter */}
          <HeadlessPopover className="relative">
            <HeadlessPopover.Button className="px-2 py-0.5 rounded-full bg-gray-100 hover:bg-gray-200 text-xs font-medium border border-gray-200 flex items-center gap-1">
              <CalendarIcon className="w-3 h-3 mr-1 text-gray-500" />
              <span>Date</span>
              {filters.dateFrom && filters.dateTo && (
                <span className="ml-1 text-xs text-blue-600">
                  ({format(filters.dateFrom, "dd/MM/yyyy")} ~ {format(filters.dateTo, "dd/MM/yyyy")})
                </span>
              )}
            </HeadlessPopover.Button>
            <HeadlessPopover.Panel className="absolute left-0 mt-2 bg-white border border-gray-200 rounded shadow-lg p-2 z-50">
              <DateRange
                ranges={[{
                  startDate: filters.dateFrom || new Date(),
                  endDate: filters.dateTo || new Date(),
                  key: "selection",
                }]}
                onChange={(item) => {
                  const { startDate, endDate } = item.selection;
                  if (startDate && endDate) {
                    updateFilters({ dateFrom: startDate, dateTo: endDate });
                  } else {
                    updateFilters({ dateFrom: undefined, dateTo: undefined });
                  }
                }}
                moveRangeOnFirstSelection={false}
                showDateDisplay={false}
              />
              <div className="flex justify-between items-center p-2">
                <button
                  onClick={() => updateFilters({ dateFrom: undefined, dateTo: undefined })}
                  className="text-xs text-red-500 hover:underline"
                >
                  Clear
                </button>
                <HeadlessPopover.Button
                  as="button"
                  type="button"
                  className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  OK
                </HeadlessPopover.Button>
              </div>
            </HeadlessPopover.Panel>
          </HeadlessPopover>

          {/* Status Filter */}
          <HeadlessPopover className="relative">
            <HeadlessPopover.Button className="px-2 py-0.5 rounded-full bg-gray-100 hover:bg-gray-200 text-xs font-medium border border-gray-200 flex items-center gap-1">
              <Flag className="w-3 h-3 mr-1 text-gray-500" />
              <span>Status</span>
              {filters.status && filters.status !== "all" && (
                <span className="ml-1 text-xs text-blue-600">
                  ({filters.status === "active" ? "Active" : "Deleted"})
                </span>
              )}
            </HeadlessPopover.Button>
            <HeadlessPopover.Panel className="absolute left-0 mt-2 w-44 bg-white border border-gray-200 rounded shadow-lg p-2 z-50">
              <div className="space-y-2">
                {statusOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.value}`}
                      checked={filters.status === option.value}
                      onChange={() => updateFilters({ status: option.value as any })}
                    />
                    <label htmlFor={`status-${option.value}`} className="text-sm">
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
              {filters.status !== "all" && (
                <div className="mt-2 text-right">
                  <button
                    onClick={() => updateFilters({ status: "all" })}
                    className="text-xs text-red-500 hover:underline"
                  >
                    Clear
                  </button>
                </div>
              )}
            </HeadlessPopover.Panel>
          </HeadlessPopover>
        </div>

        {/* Clear All Filters */}
        <div className="flex justify-end mt-3">
          <button
            onClick={clearFilters}
            className="text-xs text-gray-600 hover:text-red-600 hover:underline"
          >
            Clear all filters
          </button>
        </div>
      </div>
    </div>
  );
}
