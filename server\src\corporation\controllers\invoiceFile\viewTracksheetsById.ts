import { handleError } from "../../../utils/helpers";

export const viewTracksheetsById = async (req, res) => {
    try {
        const { id } = req.params;
        const data = await prisma.invoiceFile.findUnique({
            where: {
                id: id,
            },
            include: {
                TrackSheets: {
                    include: {
                        TrackSheetCustomFieldMapping: true,
                    },
                },
            },
        });
        
        if (!data) {
            return res.status(404).json({ message: "Invoice file not found" });
        }
        
        return res.status(200).json({ data: data.TrackSheets });
    } catch (error) {
        return handleError(res, error);
    }
};